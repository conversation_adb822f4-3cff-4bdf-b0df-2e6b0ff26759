extends GameResource
class_name ItemResource

## Item resource for weapons, armor, consumables, and quest items
##
## Defines item properties, stats, and effects

@export var item_type: ItemType = ItemType.MISC
@export var rarity: Rarity = Rarity.COMMON
@export var value: int = 1
@export var weight: float = 1.0
@export var stack_size: int = 1
@export var is_cursed: bool = false
@export var corruption_level: int = 0

# Equipment properties
@export var equipment_slot: EquipmentSlot = EquipmentSlot.NONE
@export var stat_bonuses: Dictionary = {}  # stat_name: bonus_value
@export var damage_min: int = 0
@export var damage_max: int = 0
@export var armor_value: int = 0
@export var durability: int = 100
@export var max_durability: int = 100

# Requirements
@export var level_requirement: int = 1
@export var stat_requirements: Dictionary = {}  # stat_name: required_value
@export var alignment_requirement: int = 0  # Required alignment (-100 to 100)

# Effects and abilities
@export var use_effects: Array[Dictionary] = []  # Effects when used/consumed
@export var passive_effects: Array[Dictionary] = []  # Passive effects when equipped
@export var special_abilities: Array[String] = []  # Special ability IDs

# Crafting and upgrade
@export var can_be_upgraded: bool = false
@export var upgrade_materials: Array[String] = []
@export var crafting_recipe: Array[Dictionary] = []

# Visual and audio
@export var model_path: String = ""
@export var use_sound: String = ""
@export var equip_sound: String = ""

enum ItemType {
	WEAPON,
	ARMOR,
	ACCESSORY,
	CONSUMABLE,
	QUEST_ITEM,
	CRAFTING_MATERIAL,
	MISC
}

enum Rarity {
	COMMON,
	UNCOMMON,
	RARE,
	EPIC,
	LEGENDARY,
	ARTIFACT
}

enum EquipmentSlot {
	NONE,
	MAIN_HAND,
	OFF_HAND,
	TWO_HANDED,
	HEAD,
	CHEST,
	LEGS,
	FEET,
	HANDS,
	RING,
	NECKLACE,
	CLOAK
}

func _init(item_id: String = "", name: String = "", desc: String = ""):
	super(item_id, name, desc)

func can_be_equipped() -> bool:
	"""Check if this item can be equipped"""
	return equipment_slot != EquipmentSlot.NONE

func can_be_used() -> bool:
	"""Check if this item can be used/consumed"""
	return item_type == ItemType.CONSUMABLE or use_effects.size() > 0

func can_stack() -> bool:
	"""Check if this item can stack"""
	return stack_size > 1

func get_damage_range() -> String:
	"""Get damage range as a string"""
	if damage_min > 0 or damage_max > 0:
		if damage_min == damage_max:
			return str(damage_min)
		else:
			return str(damage_min) + "-" + str(damage_max)
	return ""

func get_average_damage() -> float:
	"""Get average damage"""
	if damage_max > 0:
		return (damage_min + damage_max) / 2.0
	return 0.0

func get_rarity_color() -> Color:
	"""Get the color associated with this item's rarity"""
	match rarity:
		Rarity.COMMON:
			return Color.WHITE
		Rarity.UNCOMMON:
			return Color.GREEN
		Rarity.RARE:
			return Color.BLUE
		Rarity.EPIC:
			return Color.PURPLE
		Rarity.LEGENDARY:
			return Color.ORANGE
		Rarity.ARTIFACT:
			return Color.RED
		_:
			return Color.WHITE

func get_rarity_name() -> String:
	"""Get the rarity name as a string"""
	return Rarity.keys()[rarity]

func get_equipment_slot_name() -> String:
	"""Get the equipment slot name as a string"""
	return EquipmentSlot.keys()[equipment_slot].replace("_", " ").capitalize()

func meets_requirements(character: CharacterResource) -> bool:
	"""Check if a character meets the requirements to use this item"""
	# Level requirement
	if character.level < level_requirement:
		return false
	
	# Stat requirements
	for stat_name in stat_requirements:
		var required_value = stat_requirements[stat_name]
		var character_stat = character.get_total_stat(stat_name)
		if character_stat < required_value:
			return false
	
	# Alignment requirement (if any)
	if alignment_requirement != 0:
		if alignment_requirement > 0 and character.alignment < alignment_requirement:
			return false
		elif alignment_requirement < 0 and character.alignment > alignment_requirement:
			return false
	
	return true

func get_requirements_text() -> String:
	"""Get requirements as formatted text"""
	var requirements = []
	
	if level_requirement > 1:
		requirements.append("Level " + str(level_requirement))
	
	for stat_name in stat_requirements:
		var value = stat_requirements[stat_name]
		requirements.append(stat_name.capitalize() + " " + str(value))
	
	if alignment_requirement > 0:
		requirements.append("Good alignment (" + str(alignment_requirement) + "+)")
	elif alignment_requirement < 0:
		requirements.append("Evil alignment (" + str(alignment_requirement) + "-)")
	
	return ", ".join(requirements)

func apply_use_effects(character: CharacterResource):
	"""Apply the effects of using this item"""
	for effect in use_effects:
		apply_effect(effect, character)

func apply_effect(effect: Dictionary, character: CharacterResource):
	"""Apply a single effect to a character"""
	var effect_type = effect.get("type", "")
	var value = effect.get("value", 0)
	
	match effect_type:
		"heal":
			# Heal character (would need health system)
			pass
		"restore_stamina":
			# Restore stamina
			pass
		"restore_mana":
			# Restore mana
			pass
		"buff_stat":
			# Temporary stat buff
			var stat = effect.get("stat", "")
			var duration = effect.get("duration", 60)
			# Apply temporary buff
		"corruption":
			character.modify_corruption(value)
		"alignment":
			character.modify_alignment(value)

func get_stat_bonus(stat_name: String) -> int:
	"""Get the bonus this item provides to a specific stat"""
	return stat_bonuses.get(stat_name, 0)

func get_total_stat_bonuses() -> Dictionary:
	"""Get all stat bonuses this item provides"""
	return stat_bonuses.duplicate()

func repair(amount: int = -1):
	"""Repair the item"""
	if amount == -1:
		durability = max_durability
	else:
		durability = min(durability + amount, max_durability)

func damage_item(amount: int = 1):
	"""Damage the item's durability"""
	durability = max(0, durability - amount)

func is_broken() -> bool:
	"""Check if the item is broken"""
	return durability <= 0

func get_durability_percentage() -> float:
	"""Get durability as a percentage"""
	if max_durability <= 0:
		return 1.0
	return float(durability) / float(max_durability)

func get_tooltip_text() -> String:
	"""Get formatted tooltip text for this item"""
	var tooltip = display_name + "\n"
	tooltip += get_rarity_name() + " " + ItemType.keys()[item_type].capitalize() + "\n\n"
	
	if description != "":
		tooltip += description + "\n\n"
	
	# Damage/Armor
	if damage_min > 0 or damage_max > 0:
		tooltip += "Damage: " + get_damage_range() + "\n"
	if armor_value > 0:
		tooltip += "Armor: " + str(armor_value) + "\n"
	
	# Stat bonuses
	if stat_bonuses.size() > 0:
		tooltip += "\nBonuses:\n"
		for stat in stat_bonuses:
			var bonus = stat_bonuses[stat]
			var sign = "+" if bonus >= 0 else ""
			tooltip += sign + str(bonus) + " " + stat.capitalize() + "\n"
	
	# Requirements
	var req_text = get_requirements_text()
	if req_text != "":
		tooltip += "\nRequires: " + req_text + "\n"
	
	# Value and weight
	tooltip += "\nValue: " + str(value) + " gold"
	if weight > 0:
		tooltip += " | Weight: " + str(weight) + " lbs"
	
	# Durability
	if max_durability > 0:
		tooltip += "\nDurability: " + str(durability) + "/" + str(max_durability)
	
	# Cursed warning
	if is_cursed:
		tooltip += "\n\n[color=red]CURSED ITEM[/color]"
	
	return tooltip

func to_dictionary() -> Dictionary:
	"""Convert to dictionary for saving"""
	var data = super.to_dictionary()
	data.merge({
		"item_type": item_type,
		"rarity": rarity,
		"value": value,
		"weight": weight,
		"stack_size": stack_size,
		"is_cursed": is_cursed,
		"corruption_level": corruption_level,
		"equipment_slot": equipment_slot,
		"stat_bonuses": stat_bonuses,
		"damage_min": damage_min,
		"damage_max": damage_max,
		"armor_value": armor_value,
		"durability": durability,
		"max_durability": max_durability,
		"level_requirement": level_requirement,
		"stat_requirements": stat_requirements,
		"alignment_requirement": alignment_requirement,
		"use_effects": use_effects,
		"passive_effects": passive_effects,
		"special_abilities": special_abilities,
		"can_be_upgraded": can_be_upgraded,
		"upgrade_materials": upgrade_materials,
		"crafting_recipe": crafting_recipe,
		"model_path": model_path,
		"use_sound": use_sound,
		"equip_sound": equip_sound
	})
	return data

func from_dictionary(data: Dictionary):
	"""Load from dictionary"""
	super.from_dictionary(data)
	item_type = data.get("item_type", ItemType.MISC)
	rarity = data.get("rarity", Rarity.COMMON)
	value = data.get("value", 1)
	weight = data.get("weight", 1.0)
	stack_size = data.get("stack_size", 1)
	is_cursed = data.get("is_cursed", false)
	corruption_level = data.get("corruption_level", 0)
	equipment_slot = data.get("equipment_slot", EquipmentSlot.NONE)
	stat_bonuses = data.get("stat_bonuses", {})
	damage_min = data.get("damage_min", 0)
	damage_max = data.get("damage_max", 0)
	armor_value = data.get("armor_value", 0)
	durability = data.get("durability", 100)
	max_durability = data.get("max_durability", 100)
	level_requirement = data.get("level_requirement", 1)
	stat_requirements = data.get("stat_requirements", {})
	alignment_requirement = data.get("alignment_requirement", 0)
	use_effects = data.get("use_effects", [])
	passive_effects = data.get("passive_effects", [])
	special_abilities = data.get("special_abilities", [])
	can_be_upgraded = data.get("can_be_upgraded", false)
	upgrade_materials = data.get("upgrade_materials", [])
	crafting_recipe = data.get("crafting_recipe", [])
	model_path = data.get("model_path", "")
	use_sound = data.get("use_sound", "")
	equip_sound = data.get("equip_sound", "")
