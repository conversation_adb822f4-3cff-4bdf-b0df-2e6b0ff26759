{"id": "ashfallow_moor", "display_name": "Ashfallow Moor", "description": "A desolate moorland shrouded in perpetual mist, where ancient burial mounds dot the landscape and the very ground seems to whisper with the voices of the dead. The starting region of Eldrath, where <PERSON><PERSON><PERSON>'s journey begins.", "icon_path": "res://assets/textures/regions/ashfallow_moor_icon.png", "region_type": 0, "difficulty_level": 1, "recommended_level": 1, "is_discovered": true, "is_accessible": true, "background_music": "grimhallow", "ambient_sound": "wind", "weather_type": 2, "time_of_day": 3, "lighting_mood": 3, "towns": ["grimhallow"], "dungeons": ["sunken_crypt"], "points_of_interest": ["ancient_stone_circle", "abandoned_watchtower", "cursed_well"], "travel_connections": ["veltwyr_wilds"], "npcs": ["elder_thorne", "merchant_gareth", "blacksmith_mira", "tavern_keeper_aldric", "mysterious_stranger"], "enemy_spawns": [{"enemy_id": "shadow_wraith", "spawn_chance": 0.3, "min_level": 1, "max_level": 3, "area_type": "moor", "time_restrictions": ["dusk", "night"]}, {"enemy_id": "corrupted_wolf", "spawn_chance": 0.4, "min_level": 1, "max_level": 2, "area_type": "wilderness", "time_restrictions": []}, {"enemy_id": "skeletal_warrior", "spawn_chance": 0.2, "min_level": 2, "max_level": 4, "area_type": "burial_mounds", "time_restrictions": ["night"]}], "random_encounters": [{"id": "lost_traveler", "type": "dialogue", "probability": 0.15, "description": "A lost traveler seeks directions", "rewards": ["information", "small_coin_pouch"]}, {"id": "bandit_ambush", "type": "combat", "probability": 0.1, "description": "Bandits attempt to rob you", "enemies": ["bandit_thug", "bandit_archer"], "rewards": ["bandit_loot"]}, {"id": "ancient_shrine", "type": "discovery", "probability": 0.05, "description": "You discover an ancient shrine", "rewards": ["blessing_minor_health", "experience"]}], "harvestable_resources": ["moonbell_flower", "iron_ore", "deadwood_branch", "grave_moss"], "treasure_locations": [{"id": "hidden_cache_1", "position": {"x": 150, "y": 200}, "radius": 30, "items": ["rusty_sword", "health_potion"], "requires_skill": "perception", "skill_level": 1}, {"id": "buried_chest", "position": {"x": 300, "y": 450}, "radius": 25, "items": ["silver_coins", "leather_boots"], "requires_tool": "shovel"}], "merchant_inventories": {"merchant_gareth": ["health_potion", "stamina_potion", "iron_sword", "leather_armor", "travel_rations"], "blacksmith_mira": ["iron_sword", "iron_dagger", "leather_armor", "iron_shield", "repair_kit"]}, "main_quest_flags": [], "side_quests": ["the_missing_merchant", "cleanse_the_well", "gather_moonbell_flowers", "investigate_strange_lights"], "story_events": [{"id": "first_vision", "trigger": "enter_region", "conditions": ["first_time"], "description": "<PERSON><PERSON><PERSON> experiences his first vision of <PERSON><PERSON><PERSON><PERSON>", "script": "res://scripts/events/first_vision.gd"}, {"id": "elder_meeting", "trigger": "enter_grimhallow", "conditions": ["first_time"], "description": "Meeting with <PERSON>", "script": "res://scripts/events/elder_meeting.gd"}], "corruption_level": 15, "danger_level": 2, "magical_influence": -10, "metadata": {"version": "1.0", "created_by": "game_designer", "last_modified": "2024-01-15", "notes": "Starting region - tutorial area with basic enemies and introduction to game mechanics"}}