extends Node

## SceneManager - <PERSON>les scene transitions and loading
##
## This manager handles:
## - Scene transitions with loading screens
## - Scene preloading for performance
## - Transition effects (fade, slide, etc.)
## - Scene history for back navigation

signal scene_changed(scene_name: String)
signal scene_loading_started(scene_name: String)
signal scene_loading_finished(scene_name: String)

# Scene paths - organized by category
var scene_paths: Dictionary = {
	# Menu scenes
	"main_menu": "res://scenes/menus/main_menu.tscn",
	"settings_menu": "res://scenes/menus/settings_menu.tscn",
	"pause_menu": "res://scenes/menus/pause_menu.tscn",
	"inventory_menu": "res://scenes/ui/inventory.tscn",
	
	# World scenes
	"grimhallow": "res://scenes/environments/grimhallow.tscn",
	"ebonwatch": "res://scenes/environments/ebonwatch.tscn",
	"saltspire": "res://scenes/environments/saltspire.tscn",
	"mistmoor_keep": "res://scenes/environments/mistmoor_keep.tscn",
	"embergarde": "res://scenes/environments/embergarde.tscn",
	
	# Dungeon scenes
	"sunken_crypt": "res://scenes/dungeons/sunken_crypt.tscn",
	"hollowheart_grove": "res://scenes/dungeons/hollowheart_grove.tscn",
	"blackwave_caverns": "res://scenes/dungeons/blackwave_caverns.tscn",
	"sanctum_whispers": "res://scenes/dungeons/sanctum_whispers.tscn",
	"heart_of_ruin": "res://scenes/dungeons/heart_of_ruin.tscn",
	
	# Special scenes
	"loading_screen": "res://scenes/ui/loading_screen.tscn",
	"game_over": "res://scenes/ui/game_over.tscn"
}

# Current scene info
var current_scene_name: String = ""
var current_scene: Node = null
var scene_history: Array[String] = []

# Preloaded scenes for faster transitions
var preloaded_scenes: Dictionary = {}

# Transition settings
var transition_duration: float = 0.5
var is_transitioning: bool = false

func _ready():
	print("SceneManager initialized")
	current_scene = get_tree().current_scene
	if current_scene:
		current_scene_name = current_scene.scene_file_path.get_file().get_basename()

func change_scene(scene_name: String, use_transition: bool = true):
	"""Change to a new scene with optional transition effect"""
	if is_transitioning:
		print("Scene transition already in progress")
		return
		
	if not scene_paths.has(scene_name):
		print("Error: Scene '", scene_name, "' not found in scene_paths")
		return
	
	print("Changing scene to: ", scene_name)
	scene_loading_started.emit(scene_name)
	
	if use_transition:
		await transition_out()
	
	# Add current scene to history (if it's not a menu)
	if current_scene_name != "" and not is_menu_scene(current_scene_name):
		scene_history.append(current_scene_name)
		# Keep history manageable
		if scene_history.size() > 10:
			scene_history.pop_front()
	
	# Load the new scene
	var scene_path = scene_paths[scene_name]
	var new_scene: PackedScene
	
	# Check if scene is preloaded
	if preloaded_scenes.has(scene_name):
		new_scene = preloaded_scenes[scene_name]
		print("Using preloaded scene: ", scene_name)
	else:
		new_scene = load(scene_path)
		if not new_scene:
			print("Error: Could not load scene at path: ", scene_path)
			return
	
	# Switch scenes
	if current_scene:
		current_scene.queue_free()
	
	current_scene = new_scene.instantiate()
	get_tree().root.add_child(current_scene)
	get_tree().current_scene = current_scene
	
	current_scene_name = scene_name
	
	# Update game state based on scene type
	update_game_state_for_scene(scene_name)
	
	if use_transition:
		await transition_in()
	
	scene_loading_finished.emit(scene_name)
	scene_changed.emit(scene_name)
	print("Scene changed to: ", scene_name)

func go_back():
	"""Go back to the previous scene in history"""
	if scene_history.size() > 0:
		var previous_scene = scene_history.pop_back()
		change_scene(previous_scene)
	else:
		print("No previous scene in history")

func preload_scene(scene_name: String):
	"""Preload a scene for faster transitions"""
	if not scene_paths.has(scene_name):
		print("Error: Scene '", scene_name, "' not found in scene_paths")
		return
		
	if preloaded_scenes.has(scene_name):
		print("Scene '", scene_name, "' already preloaded")
		return
	
	var scene_path = scene_paths[scene_name]
	var scene = load(scene_path)
	if scene:
		preloaded_scenes[scene_name] = scene
		print("Preloaded scene: ", scene_name)
	else:
		print("Error: Could not preload scene at path: ", scene_path)

func unload_preloaded_scene(scene_name: String):
	"""Unload a preloaded scene to free memory"""
	if preloaded_scenes.has(scene_name):
		preloaded_scenes.erase(scene_name)
		print("Unloaded preloaded scene: ", scene_name)

func is_menu_scene(scene_name: String) -> bool:
	"""Check if a scene is a menu scene"""
	return scene_name.ends_with("_menu") or scene_name == "main_menu"

func update_game_state_for_scene(scene_name: String):
	"""Update GameManager state based on the current scene"""
	if is_menu_scene(scene_name):
		GameManager.change_game_state(GameManager.GameState.MENU)
	elif scene_name in ["sunken_crypt", "hollowheart_grove", "blackwave_caverns", "sanctum_whispers", "heart_of_ruin"]:
		GameManager.change_game_state(GameManager.GameState.DUNGEON)
	elif scene_name == "game_over":
		GameManager.change_game_state(GameManager.GameState.GAME_OVER)
	else:
		GameManager.change_game_state(GameManager.GameState.PLAYING)

func transition_out() -> void:
	"""Fade out transition effect"""
	is_transitioning = true
	var fade_overlay = create_fade_overlay()
	get_tree().root.add_child(fade_overlay)
	
	var tween = create_tween()
	tween.tween_property(fade_overlay, "modulate:a", 1.0, transition_duration)
	await tween.finished

func transition_in() -> void:
	"""Fade in transition effect"""
	var fade_overlay = get_tree().root.get_node_or_null("FadeOverlay")
	if fade_overlay:
		var tween = create_tween()
		tween.tween_property(fade_overlay, "modulate:a", 0.0, transition_duration)
		await tween.finished
		fade_overlay.queue_free()
	
	is_transitioning = false

func create_fade_overlay() -> ColorRect:
	"""Create a fade overlay for transitions"""
	var overlay = ColorRect.new()
	overlay.name = "FadeOverlay"
	overlay.color = Color.BLACK
	overlay.modulate.a = 0.0
	overlay.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	overlay.z_index = 1000  # Make sure it's on top
	return overlay

func get_current_scene_name() -> String:
	"""Get the name of the current scene"""
	return current_scene_name

func reload_current_scene():
	"""Reload the current scene"""
	if current_scene_name != "":
		change_scene(current_scene_name)

# Preload commonly used scenes on startup
func preload_common_scenes():
	"""Preload scenes that are frequently accessed"""
	var common_scenes = ["pause_menu", "inventory_menu", "loading_screen"]
	for scene_name in common_scenes:
		preload_scene(scene_name)
