extends Resource
class_name GameResource

## Base class for all game resources
##
## This provides common functionality for all game data resources:
## - Unique ID system
## - Name and description
## - Validation
## - Serialization helpers

@export var id: String = ""
@export var display_name: String = ""
@export var description: String = ""
@export var icon_path: String = ""

func _init(resource_id: String = "", name: String = "", desc: String = ""):
	"""Initialize the resource with basic data"""
	id = resource_id
	display_name = name
	description = desc

func is_valid() -> bool:
	"""Check if this resource has valid data"""
	return id != "" and display_name != ""

func get_icon() -> Texture2D:
	"""Get the icon texture for this resource"""
	if icon_path != "" and ResourceLoader.exists(icon_path):
		return load(icon_path)
	return null

func to_dictionary() -> Dictionary:
	"""Convert this resource to a dictionary for saving"""
	return {
		"id": id,
		"display_name": display_name,
		"description": description,
		"icon_path": icon_path
	}

func from_dictionary(data: Dictionary):
	"""Load this resource from a dictionary"""
	id = data.get("id", "")
	display_name = data.get("display_name", "")
	description = data.get("description", "")
	icon_path = data.get("icon_path", "")

func duplicate_resource() -> GameResource:
	"""Create a duplicate of this resource"""
	var new_resource = GameResource.new()
	new_resource.from_dictionary(to_dictionary())
	return new_resource
