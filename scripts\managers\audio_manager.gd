extends Node

## AudioManager - Handles all audio in the game
##
## This manager handles:
## - Background music with smooth transitions
## - Sound effects with pooling for performance
## - Volume control and audio settings
## - Audio zones and environmental audio
## - Combat audio and feedback

signal audio_settings_changed()

# Audio players for different types of audio
var music_player: AudioStreamPlayer
var ambient_player: AudioStreamPlayer
var sfx_players: Array[AudioStreamPlayer] = []
var voice_player: AudioStreamPlayer

# Audio settings (synced with GameManager)
var master_volume: float = 1.0
var music_volume: float = 0.8
var sfx_volume: float = 1.0
var ambient_volume: float = 0.6

# Current audio state
var current_music: String = ""
var current_ambient: String = ""
var is_music_fading: bool = false

# Audio file paths organized by type
var music_tracks: Dictionary = {
	"main_menu": "res://assets/audio/music/main_menu.ogg",
	"grimhallow": "res://assets/audio/music/grimhallow_theme.ogg",
	"dungeon_ambient": "res://assets/audio/music/dungeon_ambient.ogg",
	"combat": "res://assets/audio/music/combat_theme.ogg",
	"boss_fight": "res://assets/audio/music/boss_theme.ogg",
	"victory": "res://assets/audio/music/victory.ogg",
	"game_over": "res://assets/audio/music/game_over.ogg"
}

var ambient_sounds: Dictionary = {
	"wind": "res://assets/audio/ambient/wind.ogg",
	"dungeon_drips": "res://assets/audio/ambient/dungeon_drips.ogg",
	"forest": "res://assets/audio/ambient/forest.ogg",
	"ocean": "res://assets/audio/ambient/ocean_waves.ogg",
	"fire": "res://assets/audio/ambient/crackling_fire.ogg"
}

var sfx_sounds: Dictionary = {
	# UI sounds
	"button_click": "res://assets/audio/sfx/ui_click.ogg",
	"button_hover": "res://assets/audio/sfx/ui_hover.ogg",
	"menu_open": "res://assets/audio/sfx/menu_open.ogg",
	"menu_close": "res://assets/audio/sfx/menu_close.ogg",
	
	# Combat sounds
	"sword_swing": "res://assets/audio/sfx/sword_swing.ogg",
	"sword_hit": "res://assets/audio/sfx/sword_hit.ogg",
	"block": "res://assets/audio/sfx/block.ogg",
	"dodge": "res://assets/audio/sfx/dodge.ogg",
	"spell_cast": "res://assets/audio/sfx/spell_cast.ogg",
	"magic_hit": "res://assets/audio/sfx/magic_hit.ogg",
	
	# Character sounds
	"footstep": "res://assets/audio/sfx/footstep.ogg",
	"hurt": "res://assets/audio/sfx/player_hurt.ogg",
	"death": "res://assets/audio/sfx/player_death.ogg",
	"level_up": "res://assets/audio/sfx/level_up.ogg",
	
	# World sounds
	"door_open": "res://assets/audio/sfx/door_open.ogg",
	"chest_open": "res://assets/audio/sfx/chest_open.ogg",
	"item_pickup": "res://assets/audio/sfx/item_pickup.ogg",
	"coin_pickup": "res://assets/audio/sfx/coin_pickup.ogg"
}

func _ready():
	print("AudioManager initialized")
	setup_audio_players()
	connect_to_game_manager()
	update_volumes()

func setup_audio_players():
	"""Set up the audio players"""
	# Music player
	music_player = AudioStreamPlayer.new()
	music_player.name = "MusicPlayer"
	music_player.bus = "Music"
	add_child(music_player)
	
	# Ambient player
	ambient_player = AudioStreamPlayer.new()
	ambient_player.name = "AmbientPlayer"
	ambient_player.bus = "Ambient"
	add_child(ambient_player)
	
	# Voice player for dialogue
	voice_player = AudioStreamPlayer.new()
	voice_player.name = "VoicePlayer"
	voice_player.bus = "Voice"
	add_child(voice_player)
	
	# Create a pool of SFX players for overlapping sounds
	for i in range(10):
		var sfx_player = AudioStreamPlayer.new()
		sfx_player.name = "SFXPlayer" + str(i)
		sfx_player.bus = "SFX"
		sfx_players.append(sfx_player)
		add_child(sfx_player)

func connect_to_game_manager():
	"""Connect to GameManager signals"""
	if GameManager:
		GameManager.game_state_changed.connect(_on_game_state_changed)

func _on_game_state_changed(new_state: GameManager.GameState):
	"""Handle game state changes for audio"""
	match new_state:
		GameManager.GameState.MENU:
			play_music("main_menu")
		GameManager.GameState.PLAYING:
			# Music will be set by the specific scene
			pass
		GameManager.GameState.PAUSED:
			pause_all_audio()
		GameManager.GameState.GAME_OVER:
			play_music("game_over")

func play_music(track_name: String, fade_in: bool = true):
	"""Play a music track with optional fade in"""
	if current_music == track_name and music_player.playing:
		return  # Already playing this track
	
	if not music_tracks.has(track_name):
		print("Error: Music track '", track_name, "' not found")
		return
	
	print("Playing music: ", track_name)
	
	if fade_in and music_player.playing:
		await fade_out_music()
	
	var audio_stream = load(music_tracks[track_name])
	if audio_stream:
		music_player.stream = audio_stream
		current_music = track_name
		
		if fade_in:
			music_player.volume_db = -80
			music_player.play()
			await fade_in_music()
		else:
			music_player.play()

func stop_music(fade_out: bool = true):
	"""Stop the current music"""
	if fade_out:
		await fade_out_music()
	else:
		music_player.stop()
	current_music = ""

func fade_in_music():
	"""Fade in the current music"""
	if is_music_fading:
		return
	
	is_music_fading = true
	var target_volume = linear_to_db(music_volume * master_volume)
	var tween = create_tween()
	tween.tween_property(music_player, "volume_db", target_volume, 1.0)
	await tween.finished
	is_music_fading = false

func fade_out_music():
	"""Fade out the current music"""
	if is_music_fading:
		return
	
	is_music_fading = true
	var tween = create_tween()
	tween.tween_property(music_player, "volume_db", -80, 1.0)
	await tween.finished
	music_player.stop()
	is_music_fading = false

func play_ambient(sound_name: String, loop: bool = true):
	"""Play an ambient sound"""
	if not ambient_sounds.has(sound_name):
		print("Error: Ambient sound '", sound_name, "' not found")
		return
	
	var audio_stream = load(ambient_sounds[sound_name])
	if audio_stream:
		ambient_player.stream = audio_stream
		if audio_stream is AudioStreamOggVorbis:
			audio_stream.loop = loop
		ambient_player.play()
		current_ambient = sound_name
		print("Playing ambient: ", sound_name)

func stop_ambient():
	"""Stop the current ambient sound"""
	ambient_player.stop()
	current_ambient = ""

func play_sfx(sound_name: String, volume_modifier: float = 1.0):
	"""Play a sound effect"""
	if not sfx_sounds.has(sound_name):
		print("Error: SFX '", sound_name, "' not found")
		return
	
	# Find an available SFX player
	var available_player: AudioStreamPlayer = null
	for player in sfx_players:
		if not player.playing:
			available_player = player
			break
	
	if not available_player:
		# All players busy, use the first one (interrupt)
		available_player = sfx_players[0]
	
	var audio_stream = load(sfx_sounds[sound_name])
	if audio_stream:
		available_player.stream = audio_stream
		available_player.volume_db = linear_to_db(sfx_volume * master_volume * volume_modifier)
		available_player.play()

func play_voice(audio_stream: AudioStream):
	"""Play a voice line (for dialogue)"""
	voice_player.stream = audio_stream
	voice_player.play()

func stop_voice():
	"""Stop the current voice line"""
	voice_player.stop()

func pause_all_audio():
	"""Pause all audio"""
	music_player.stream_paused = true
	ambient_player.stream_paused = true
	voice_player.stream_paused = true
	for player in sfx_players:
		player.stream_paused = true

func resume_all_audio():
	"""Resume all audio"""
	music_player.stream_paused = false
	ambient_player.stream_paused = false
	voice_player.stream_paused = false
	for player in sfx_players:
		player.stream_paused = false

func update_volumes():
	"""Update all volume levels"""
	# Get volumes from GameManager
	master_volume = GameManager.game_settings.get("master_volume", 1.0)
	music_volume = GameManager.game_settings.get("music_volume", 0.8)
	sfx_volume = GameManager.game_settings.get("sfx_volume", 1.0)
	
	# Apply volumes to audio buses
	AudioServer.set_bus_volume_db(AudioServer.get_bus_index("Master"), linear_to_db(master_volume))
	AudioServer.set_bus_volume_db(AudioServer.get_bus_index("Music"), linear_to_db(music_volume))
	AudioServer.set_bus_volume_db(AudioServer.get_bus_index("SFX"), linear_to_db(sfx_volume))
	
	audio_settings_changed.emit()

func set_master_volume(volume: float):
	"""Set master volume"""
	master_volume = clamp(volume, 0.0, 1.0)
	GameManager.game_settings.master_volume = master_volume
	update_volumes()

func set_music_volume(volume: float):
	"""Set music volume"""
	music_volume = clamp(volume, 0.0, 1.0)
	GameManager.game_settings.music_volume = music_volume
	update_volumes()

func set_sfx_volume(volume: float):
	"""Set SFX volume"""
	sfx_volume = clamp(volume, 0.0, 1.0)
	GameManager.game_settings.sfx_volume = sfx_volume
	update_volumes()
