extends Node

## InputManager - <PERSON><PERSON> input mapping and control schemes
##
## This manager handles:
## - Input action mapping and remapping
## - Control scheme switching (keyboard/gamepad)
## - Input buffering for combat
## - Context-sensitive input handling

signal input_device_changed(device_type: String)
signal control_scheme_changed(scheme: String)

enum InputDevice {
	KEYBOARD_MOUSE,
	GAMEPAD
}

enum InputContext {
	MENU,
	GAMEPLAY,
	COMBAT,
	DIA<PERSON>OGUE,
	INVENTORY
}

# Current input state
var current_device: InputDevice = InputDevice.KEYBOARD_MOUSE
var current_context: InputContext = InputContext.MENU
var input_enabled: bool = true

# Input buffer for combat (stores recent inputs)
var input_buffer: Array = []
var buffer_duration: float = 0.5  # How long to keep inputs in buffer
var max_buffer_size: int = 10

# Default input mappings
var default_actions: Dictionary = {
	# Movement
	"move_up": [KEY_W, KEY_UP],
	"move_down": [<PERSON>EY_<PERSON>, KEY_DOWN], 
	"move_left": [KEY_A, KEY_LEFT],
	"move_right": [KEY_D, KEY_RIGHT],
	"run": [KEY_SHIFT],
	
	# Combat
	"attack": [MOUSE_BUTTON_LEFT],
	"heavy_attack": [MOUSE_BUTTON_RIGHT],
	"block": [KEY_Q],
	"dodge": [KEY_SPACE],
	"cast_spell": [KEY_E],
	
	# Interaction
	"interact": [KEY_F],
	"pickup": [KEY_G],
	
	# UI
	"inventory": [KEY_I, KEY_TAB],
	"character_sheet": [KEY_C],
	"pause": [KEY_ESCAPE],
	"confirm": [KEY_ENTER, MOUSE_BUTTON_LEFT],
	"cancel": [KEY_ESCAPE, MOUSE_BUTTON_RIGHT],
	
	# Camera
	"camera_reset": [KEY_R]
}

# Gamepad mappings
var gamepad_actions: Dictionary = {
	"move_up": [JOY_BUTTON_DPAD_UP],
	"move_down": [JOY_BUTTON_DPAD_DOWN],
	"move_left": [JOY_BUTTON_DPAD_LEFT], 
	"move_right": [JOY_BUTTON_DPAD_RIGHT],
	"run": [JOY_BUTTON_LEFT_SHOULDER],
	
	"attack": [JOY_BUTTON_X],
	"heavy_attack": [JOY_BUTTON_Y],
	"block": [JOY_BUTTON_LEFT_SHOULDER],
	"dodge": [JOY_BUTTON_A],
	"cast_spell": [JOY_BUTTON_B],
	
	"interact": [JOY_BUTTON_X],
	"pickup": [JOY_BUTTON_Y],
	
	"inventory": [JOY_BUTTON_BACK],
	"character_sheet": [JOY_BUTTON_RIGHT_STICK],
	"pause": [JOY_BUTTON_START],
	"confirm": [JOY_BUTTON_A],
	"cancel": [JOY_BUTTON_B]
}

func _ready():
	print("InputManager initialized")
	setup_default_input_map()
	connect_signals()

func _input(event):
	"""Handle input events and device detection"""
	if not input_enabled:
		return
	
	# Detect input device changes
	detect_input_device(event)
	
	# Add to input buffer if it's an action event
	if event is InputEventKey or event is InputEventMouseButton or event is InputEventJoypadButton:
		add_to_input_buffer(event)
	
	# Handle context-specific input
	handle_context_input(event)

func detect_input_device(event):
	"""Detect which input device is being used"""
	var new_device = current_device
	
	if event is InputEventKey or event is InputEventMouseButton or event is InputEventMouseMotion:
		new_device = InputDevice.KEYBOARD_MOUSE
	elif event is InputEventJoypadButton or event is InputEventJoypadMotion:
		new_device = InputDevice.GAMEPAD
	
	if new_device != current_device:
		current_device = new_device
		var device_name = "keyboard_mouse" if new_device == InputDevice.KEYBOARD_MOUSE else "gamepad"
		print("Input device changed to: ", device_name)
		input_device_changed.emit(device_name)

func setup_default_input_map():
	"""Set up the default input action map"""
	# Clear existing actions
	for action in InputMap.get_actions():
		if not action.begins_with("ui_"):  # Keep built-in UI actions
			InputMap.erase_action(action)
	
	# Add our custom actions
	for action_name in default_actions:
		if not InputMap.has_action(action_name):
			InputMap.add_action(action_name)
		
		# Add keyboard/mouse events
		for key_code in default_actions[action_name]:
			var event: InputEvent
			if key_code >= MOUSE_BUTTON_LEFT and key_code <= MOUSE_BUTTON_XBUTTON2:
				event = InputEventMouseButton.new()
				event.button_index = key_code
			else:
				event = InputEventKey.new()
				event.keycode = key_code
			
			InputMap.action_add_event(action_name, event)
		
		# Add gamepad events if available
		if gamepad_actions.has(action_name):
			for button_code in gamepad_actions[action_name]:
				var event = InputEventJoypadButton.new()
				event.button_index = button_code
				InputMap.action_add_event(action_name, event)

func connect_signals():
	"""Connect to relevant signals"""
	if GameManager:
		GameManager.game_state_changed.connect(_on_game_state_changed)

func _on_game_state_changed(new_state: GameManager.GameState):
	"""Update input context based on game state"""
	match new_state:
		GameManager.GameState.MENU:
			set_input_context(InputContext.MENU)
		GameManager.GameState.PLAYING:
			set_input_context(InputContext.GAMEPLAY)
		GameManager.GameState.DUNGEON:
			set_input_context(InputContext.COMBAT)
		GameManager.GameState.DIALOGUE:
			set_input_context(InputContext.DIALOGUE)
		GameManager.GameState.INVENTORY:
			set_input_context(InputContext.INVENTORY)
		_:
			set_input_context(InputContext.MENU)

func set_input_context(context: InputContext):
	"""Set the current input context"""
	if current_context != context:
		current_context = context
		print("Input context changed to: ", InputContext.keys()[context])

func handle_context_input(event):
	"""Handle input based on current context"""
	match current_context:
		InputContext.MENU:
			handle_menu_input(event)
		InputContext.GAMEPLAY:
			handle_gameplay_input(event)
		InputContext.COMBAT:
			handle_combat_input(event)
		InputContext.DIALOGUE:
			handle_dialogue_input(event)
		InputContext.INVENTORY:
			handle_inventory_input(event)

func handle_menu_input(event):
	"""Handle menu-specific input"""
	if event.is_action_pressed("pause"):
		# Handle menu navigation
		pass

func handle_gameplay_input(event):
	"""Handle gameplay input"""
	if event.is_action_pressed("pause"):
		GameManager.change_game_state(GameManager.GameState.PAUSED)
	elif event.is_action_pressed("inventory"):
		GameManager.change_game_state(GameManager.GameState.INVENTORY)

func handle_combat_input(event):
	"""Handle combat-specific input"""
	# Combat input is handled by the combat system
	# This is where we might add input buffering logic
	pass

func handle_dialogue_input(event):
	"""Handle dialogue input"""
	if event.is_action_pressed("confirm") or event.is_action_pressed("interact"):
		# Advance dialogue
		pass
	elif event.is_action_pressed("cancel"):
		# Skip or exit dialogue
		pass

func handle_inventory_input(event):
	"""Handle inventory input"""
	if event.is_action_pressed("inventory") or event.is_action_pressed("cancel"):
		GameManager.change_game_state(GameManager.GameState.PLAYING)

func add_to_input_buffer(event):
	"""Add an input event to the buffer for combat combos"""
	var buffer_entry = {
		"event": event,
		"timestamp": Time.get_time_dict_from_system()
	}
	
	input_buffer.append(buffer_entry)
	
	# Clean old entries
	clean_input_buffer()
	
	# Limit buffer size
	if input_buffer.size() > max_buffer_size:
		input_buffer.pop_front()

func clean_input_buffer():
	"""Remove old entries from input buffer"""
	var current_time = Time.get_time_dict_from_system()
	var cutoff_time = current_time # This would need proper time calculation
	
	# Remove entries older than buffer_duration
	# Simplified for now - in a real implementation you'd calculate time differences
	if input_buffer.size() > 5:  # Simple size-based cleanup for now
		input_buffer = input_buffer.slice(-5)

func get_recent_inputs() -> Array:
	"""Get recent inputs for combo detection"""
	clean_input_buffer()
	return input_buffer.duplicate()

func clear_input_buffer():
	"""Clear the input buffer"""
	input_buffer.clear()

func enable_input():
	"""Enable input processing"""
	input_enabled = true

func disable_input():
	"""Disable input processing"""
	input_enabled = false

func is_action_just_pressed(action: String) -> bool:
	"""Check if an action was just pressed (with input enabled check)"""
	return input_enabled and Input.is_action_just_pressed(action)

func is_action_pressed(action: String) -> bool:
	"""Check if an action is currently pressed (with input enabled check)"""
	return input_enabled and Input.is_action_pressed(action)

func get_action_strength(action: String) -> float:
	"""Get the strength of an action (for analog inputs)"""
	if not input_enabled:
		return 0.0
	return Input.get_action_strength(action)

func get_movement_vector() -> Vector2:
	"""Get the movement input as a Vector2"""
	if not input_enabled:
		return Vector2.ZERO
	
	var movement = Vector2()
	movement.x = Input.get_action_strength("move_right") - Input.get_action_strength("move_left")
	movement.y = Input.get_action_strength("move_down") - Input.get_action_strength("move_up")
	
	return movement.normalized() if movement.length() > 0.1 else Vector2.ZERO
