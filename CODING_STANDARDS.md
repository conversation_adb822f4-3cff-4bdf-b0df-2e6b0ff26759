# Eldrath - Coding Standards

## Overview
This document establishes coding standards and conventions for the Eldrath project to ensure consistency, maintainability, and readability across the codebase.

## File Organization

### Directory Structure
```
scripts/
├── core/           # Core game systems and utilities
├── managers/       # Singleton managers (GameManager, SceneManager, etc.)
├── characters/     # Player and NPC controllers
├── combat/         # Combat system components
├── dungeons/       # Dungeon generation and management
├── world/          # World and environment systems
├── ui/             # User interface components
└── resources/      # Resource classes for game data

scenes/
├── characters/     # Character scene files
├── environments/   # World and location scenes
├── ui/             # UI scene files
├── dungeons/       # Dungeon scene files
└── menus/          # Menu scene files

data/
├── regions/        # Region data files (JSON)
├── characters/     # Character data files (JSON)
├── items/          # Item data files (JSON)
└── dungeons/       # Dungeon template data (JSON)
```

## Naming Conventions

### Files and Directories
- Use `snake_case` for all file and directory names
- Scene files: `scene_name.tscn`
- Script files: `script_name.gd`
- Resource files: `resource_name.tres` or `resource_name.json`

### Classes and Resources
- Use `PascalCase` for class names
- Suffix resource classes with `Resource` (e.g., `CharacterResource`)
- Suffix manager classes with `Manager` (e.g., `GameManager`)
- Suffix controller classes with `Controller` (e.g., `PlayerController`)

### Variables and Functions
- Use `snake_case` for variables and functions
- Use descriptive names that clearly indicate purpose
- Boolean variables should start with `is_`, `has_`, `can_`, or `should_`
- Constants use `UPPER_SNAKE_CASE`

### Signals
- Use `snake_case` for signal names
- Use descriptive names that indicate what happened
- Examples: `health_changed`, `item_picked_up`, `dialogue_started`

## Code Structure

### Script Header
Every script should start with a class documentation comment:

```gdscript
extends Node
class_name ClassName

## Brief description of what this class does
##
## Longer description explaining:
## - Main responsibilities
## - Key features
## - Usage examples
```

### Function Documentation
Document all public functions:

```gdscript
func function_name(param1: Type, param2: Type) -> ReturnType:
	"""Brief description of what this function does
	
	Args:
		param1: Description of parameter
		param2: Description of parameter
	
	Returns:
		Description of return value
	"""
	# Implementation here
```

### Variable Organization
Organize variables in this order:
1. Exported variables (@export)
2. Public variables
3. Private variables (prefixed with _)
4. Constants

```gdscript
# Exported variables
@export var max_health: int = 100
@export var speed: float = 200.0

# Public variables
var current_health: int
var is_alive: bool = true

# Private variables
var _internal_state: Dictionary = {}
var _cached_value: float

# Constants
const MAX_LEVEL: int = 50
const DEFAULT_NAME: String = "Unknown"
```

## Error Handling

### Validation
Always validate inputs and provide meaningful error messages:

```gdscript
func set_health(new_health: int):
	if new_health < 0:
		print("Error: Health cannot be negative")
		return
	
	if new_health > max_health:
		print("Warning: Health clamped to maximum")
		new_health = max_health
	
	current_health = new_health
```

### Resource Loading
Always check if resources loaded successfully:

```gdscript
func load_character_data(file_path: String) -> CharacterResource:
	if not ResourceLoader.exists(file_path):
		print("Error: Character file not found: ", file_path)
		return null
	
	var resource = load(file_path)
	if not resource is CharacterResource:
		print("Error: Invalid character resource: ", file_path)
		return null
	
	return resource
```

## Performance Guidelines

### Object Pooling
Use object pooling for frequently created/destroyed objects:

```gdscript
# For projectiles, particles, UI elements, etc.
var projectile_pool: Array[Projectile] = []

func get_projectile() -> Projectile:
	if projectile_pool.size() > 0:
		return projectile_pool.pop_back()
	else:
		return Projectile.new()

func return_projectile(projectile: Projectile):
	projectile.reset()
	projectile_pool.append(projectile)
```

### Signal Connections
Always disconnect signals when no longer needed:

```gdscript
func _ready():
	player.health_changed.connect(_on_player_health_changed)

func _exit_tree():
	if player and player.health_changed.is_connected(_on_player_health_changed):
		player.health_changed.disconnect(_on_player_health_changed)
```

## Data Management

### JSON Data Format
Use consistent JSON structure for game data:

```json
{
	"id": "unique_identifier",
	"display_name": "Human Readable Name",
	"description": "Detailed description",
	"type": "category_type",
	"properties": {
		"key": "value"
	},
	"metadata": {
		"version": "1.0",
		"created_by": "designer_name",
		"last_modified": "2024-01-01"
	}
}
```

### Resource Serialization
Implement consistent serialization for all resources:

```gdscript
func to_dictionary() -> Dictionary:
	var data = super.to_dictionary()  # Call parent if extending GameResource
	data.merge({
		"custom_property": custom_property,
		"another_property": another_property
	})
	return data

func from_dictionary(data: Dictionary):
	super.from_dictionary(data)  # Call parent if extending GameResource
	custom_property = data.get("custom_property", default_value)
	another_property = data.get("another_property", default_value)
```

## Testing Guidelines

### Unit Tests
Write tests for critical game systems:

```gdscript
# test_character_resource.gd
extends GutTest

func test_character_level_up():
	var character = CharacterResource.new("test_char", "Test Character")
	character.add_experience(100)
	
	assert_eq(character.level, 2, "Character should level up")
	assert_gt(character.max_health, 100, "Health should increase on level up")
```

### Debug Helpers
Include debug information in development builds:

```gdscript
func _ready():
	if OS.is_debug_build():
		print("Debug: ", get_class(), " initialized with ", get_children().size(), " children")
```

## Git Commit Guidelines

### Commit Message Format
```
type(scope): brief description

Longer description if needed

- List specific changes
- Use bullet points for multiple changes
```

### Commit Types
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

### Examples
```
feat(combat): add dodge roll mechanic

Implemented dodge roll with i-frames and stamina cost

- Added dodge input handling
- Created dodge animation state
- Implemented invincibility frames
- Added stamina consumption

fix(inventory): prevent item duplication bug

Fixed issue where items could be duplicated when rapidly clicking

refactor(managers): improve singleton initialization order

Ensured GameManager initializes before other managers
```

## Code Review Checklist

Before submitting code for review:

- [ ] Code follows naming conventions
- [ ] Functions are documented
- [ ] Error handling is implemented
- [ ] No hardcoded values (use constants or configuration)
- [ ] Performance considerations addressed
- [ ] Memory leaks prevented (signals disconnected, objects freed)
- [ ] Code is readable and well-organized
- [ ] Tests written for new functionality
- [ ] Debug prints removed or made conditional

## Tools and Utilities

### Recommended Plugins
- GUT (Godot Unit Testing)
- Godot Tools (VS Code extension)
- GitLens (for Git integration)

### Code Formatting
Use consistent indentation (tabs) and spacing throughout the project.

## Conclusion

Following these standards will help maintain code quality and make the project easier to understand, debug, and extend. When in doubt, prioritize readability and maintainability over clever optimizations.
