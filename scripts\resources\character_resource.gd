extends GameResource
class_name CharacterResource

## Character resource for NPCs and player data
##
## Defines character stats, abilities, and progression

@export var character_type: CharacterType = CharacterType.NPC
@export var level: int = 1
@export var experience: int = 0

# Core stats
@export var max_health: int = 100
@export var max_stamina: int = 100
@export var max_mana: int = 50

# Combat stats
@export var strength: int = 10
@export var dexterity: int = 10
@export var intelligence: int = 10
@export var constitution: int = 10
@export var wisdom: int = 10
@export var charisma: int = 10

# Derived stats (calculated from core stats)
var attack_power: int
var defense: int
var magic_power: int
var magic_resistance: int
var critical_chance: float
var dodge_chance: float

# Character state
@export var corruption: int = 0  # 0-100, affects magic and story
@export var alignment: int = 0   # -100 (evil) to +100 (good)
@export var reputation: Dictionary = {}  # Reputation with different factions

# Equipment and inventory
@export var equipped_items: Dictionary = {}
@export var inventory: Array[String] = []  # Item IDs
@export var abilities: Array[String] = []  # Ability IDs

# Character appearance and dialogue
@export var portrait_path: String = ""
@export var model_path: String = ""
@export var dialogue_tree_id: String = ""
@export var voice_set: String = ""

# Location and behavior (for NPCs)
@export var home_location: String = ""
@export var current_location: String = ""
@export var behavior_type: BehaviorType = BehaviorType.FRIENDLY
@export var shop_inventory: Array[String] = []  # For merchant NPCs

enum CharacterType {
	PLAYER,
	NPC,
	ENEMY,
	BOSS,
	COMPANION
}

enum BehaviorType {
	FRIENDLY,
	NEUTRAL,
	HOSTILE,
	MERCHANT,
	QUEST_GIVER,
	GUARD
}

func _init(char_id: String = "", name: String = "", desc: String = ""):
	super(char_id, name, desc)
	calculate_derived_stats()

func calculate_derived_stats():
	"""Calculate derived stats from core stats"""
	attack_power = strength * 2 + dexterity
	defense = constitution * 2 + strength
	magic_power = intelligence * 2 + wisdom
	magic_resistance = wisdom * 2 + constitution
	critical_chance = dexterity * 0.5
	dodge_chance = dexterity * 0.3

func get_total_stat(stat_name: String) -> int:
	"""Get a stat including equipment bonuses"""
	var base_stat = get(stat_name)
	var equipment_bonus = get_equipment_bonus(stat_name)
	return base_stat + equipment_bonus

func get_equipment_bonus(stat_name: String) -> int:
	"""Calculate equipment bonus for a stat"""
	var bonus = 0
	# This would iterate through equipped items and sum their bonuses
	# For now, return 0 as we haven't implemented items yet
	return bonus

func add_experience(amount: int):
	"""Add experience and handle level ups"""
	experience += amount
	check_level_up()

func check_level_up():
	"""Check if character should level up"""
	var required_exp = level * 100  # Simple formula
	while experience >= required_exp:
		level_up()
		required_exp = level * 100

func level_up():
	"""Handle level up"""
	level += 1
	experience -= (level - 1) * 100
	
	# Increase stats based on character type
	match character_type:
		CharacterType.PLAYER:
			max_health += 10
			max_stamina += 5
			max_mana += 3
			# Player gets to choose stat increases
		CharacterType.ENEMY, CharacterType.BOSS:
			# Enemies get balanced stat increases
			strength += 1
			constitution += 1
			max_health += 8
	
	calculate_derived_stats()

func modify_corruption(amount: int):
	"""Modify corruption level"""
	corruption = clamp(corruption + amount, 0, 100)

func modify_alignment(amount: int):
	"""Modify moral alignment"""
	alignment = clamp(alignment + amount, -100, 100)

func modify_reputation(faction: String, amount: int):
	"""Modify reputation with a faction"""
	if not reputation.has(faction):
		reputation[faction] = 0
	reputation[faction] = clamp(reputation[faction] + amount, -100, 100)

func get_reputation(faction: String) -> int:
	"""Get reputation with a faction"""
	return reputation.get(faction, 0)

func can_equip_item(item_id: String) -> bool:
	"""Check if character can equip an item"""
	# This would check item requirements against character stats
	# For now, return true
	return true

func equip_item(item_id: String, slot: String) -> bool:
	"""Equip an item to a slot"""
	if can_equip_item(item_id):
		# Unequip current item if any
		if equipped_items.has(slot):
			unequip_item(slot)
		
		equipped_items[slot] = item_id
		# Remove from inventory if it's there
		if inventory.has(item_id):
			inventory.erase(item_id)
		
		calculate_derived_stats()
		return true
	return false

func unequip_item(slot: String) -> String:
	"""Unequip an item from a slot"""
	if equipped_items.has(slot):
		var item_id = equipped_items[slot]
		equipped_items.erase(slot)
		inventory.append(item_id)
		calculate_derived_stats()
		return item_id
	return ""

func add_to_inventory(item_id: String):
	"""Add an item to inventory"""
	inventory.append(item_id)

func remove_from_inventory(item_id: String) -> bool:
	"""Remove an item from inventory"""
	if inventory.has(item_id):
		inventory.erase(item_id)
		return true
	return false

func has_item(item_id: String) -> bool:
	"""Check if character has an item (equipped or in inventory)"""
	return inventory.has(item_id) or item_id in equipped_items.values()

func learn_ability(ability_id: String):
	"""Learn a new ability"""
	if not abilities.has(ability_id):
		abilities.append(ability_id)

func forget_ability(ability_id: String):
	"""Forget an ability"""
	abilities.erase(ability_id)

func has_ability(ability_id: String) -> bool:
	"""Check if character has an ability"""
	return abilities.has(ability_id)

func is_alive() -> bool:
	"""Check if character is alive (has health > 0)"""
	return max_health > 0

func get_corruption_level() -> String:
	"""Get corruption level as a string"""
	if corruption < 20:
		return "Pure"
	elif corruption < 40:
		return "Tainted"
	elif corruption < 60:
		return "Corrupted"
	elif corruption < 80:
		return "Dark"
	else:
		return "Damned"

func get_alignment_description() -> String:
	"""Get alignment as a descriptive string"""
	if alignment > 50:
		return "Good"
	elif alignment > 20:
		return "Lawful"
	elif alignment > -20:
		return "Neutral"
	elif alignment > -50:
		return "Chaotic"
	else:
		return "Evil"

func to_dictionary() -> Dictionary:
	"""Convert to dictionary for saving"""
	var data = super.to_dictionary()
	data.merge({
		"character_type": character_type,
		"level": level,
		"experience": experience,
		"max_health": max_health,
		"max_stamina": max_stamina,
		"max_mana": max_mana,
		"strength": strength,
		"dexterity": dexterity,
		"intelligence": intelligence,
		"constitution": constitution,
		"wisdom": wisdom,
		"charisma": charisma,
		"corruption": corruption,
		"alignment": alignment,
		"reputation": reputation,
		"equipped_items": equipped_items,
		"inventory": inventory,
		"abilities": abilities,
		"portrait_path": portrait_path,
		"model_path": model_path,
		"dialogue_tree_id": dialogue_tree_id,
		"voice_set": voice_set,
		"home_location": home_location,
		"current_location": current_location,
		"behavior_type": behavior_type,
		"shop_inventory": shop_inventory
	})
	return data

func from_dictionary(data: Dictionary):
	"""Load from dictionary"""
	super.from_dictionary(data)
	character_type = data.get("character_type", CharacterType.NPC)
	level = data.get("level", 1)
	experience = data.get("experience", 0)
	max_health = data.get("max_health", 100)
	max_stamina = data.get("max_stamina", 100)
	max_mana = data.get("max_mana", 50)
	strength = data.get("strength", 10)
	dexterity = data.get("dexterity", 10)
	intelligence = data.get("intelligence", 10)
	constitution = data.get("constitution", 10)
	wisdom = data.get("wisdom", 10)
	charisma = data.get("charisma", 10)
	corruption = data.get("corruption", 0)
	alignment = data.get("alignment", 0)
	reputation = data.get("reputation", {})
	equipped_items = data.get("equipped_items", {})
	inventory = data.get("inventory", [])
	abilities = data.get("abilities", [])
	portrait_path = data.get("portrait_path", "")
	model_path = data.get("model_path", "")
	dialogue_tree_id = data.get("dialogue_tree_id", "")
	voice_set = data.get("voice_set", "")
	home_location = data.get("home_location", "")
	current_location = data.get("current_location", "")
	behavior_type = data.get("behavior_type", BehaviorType.FRIENDLY)
	shop_inventory = data.get("shop_inventory", [])
	
	calculate_derived_stats()
