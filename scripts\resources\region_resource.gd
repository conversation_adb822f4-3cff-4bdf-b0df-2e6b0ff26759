extends GameResource
class_name RegionResource

## Region resource for world areas and locations
##
## Defines regions, towns, dungeons, and their properties

@export var region_type: RegionType = RegionType.OVERWORLD
@export var difficulty_level: int = 1
@export var recommended_level: int = 1
@export var is_discovered: bool = false
@export var is_accessible: bool = true

# Visual and atmospheric properties
@export var background_music: String = ""
@export var ambient_sound: String = ""
@export var weather_type: WeatherType = WeatherType.CLEAR
@export var time_of_day: TimeOfDay = TimeOfDay.DAY
@export var lighting_mood: LightingMood = LightingMood.NEUTRAL

# Locations within this region
@export var towns: Array[String] = []  # Town IDs
@export var dungeons: Array[String] = []  # Dungeon IDs
@export var points_of_interest: Array[String] = []  # POI IDs
@export var travel_connections: Array[String] = []  # Connected region IDs

# NPCs and encounters
@export var npcs: Array[String] = []  # NPC IDs present in this region
@export var enemy_spawns: Array[Dictionary] = []  # Enemy spawn data
@export var random_encounters: Array[Dictionary] = []  # Random encounter data

# Loot and resources
@export var harvestable_resources: Array[String] = []  # Resource IDs
@export var treasure_locations: Array[Dictionary] = []  # Hidden treasure data
@export var merchant_inventories: Dictionary = {}  # NPC_ID: inventory_items

# Story and quest data
@export var main_quest_flags: Array[String] = []  # Required quest flags to access
@export var side_quests: Array[String] = []  # Available side quest IDs
@export var story_events: Array[Dictionary] = []  # Scripted story events

# Environmental effects
@export var corruption_level: int = 0  # 0-100, affects magic and encounters
@export var danger_level: int = 1  # 1-10, affects enemy strength
@export var magical_influence: int = 0  # -100 to 100, affects magic effectiveness

enum RegionType {
	OVERWORLD,
	TOWN,
	DUNGEON,
	WILDERNESS,
	RUINS,
	UNDERGROUND,
	SPECIAL
}

enum WeatherType {
	CLEAR,
	CLOUDY,
	RAIN,
	STORM,
	FOG,
	SNOW,
	BLIZZARD,
	SUPERNATURAL
}

enum TimeOfDay {
	DAWN,
	DAY,
	DUSK,
	NIGHT,
	ETERNAL_NIGHT,
	TIMELESS
}

enum LightingMood {
	BRIGHT,
	NEUTRAL,
	DIM,
	DARK,
	OMINOUS,
	MAGICAL,
	CORRUPTED
}

func _init(region_id: String = "", name: String = "", desc: String = ""):
	super(region_id, name, desc)

func can_access(character: CharacterResource) -> bool:
	"""Check if a character can access this region"""
	if not is_accessible:
		return false
	
	# Check level requirement
	if character.level < recommended_level - 2:  # Allow some flexibility
		return false
	
	# Check quest flags
	for flag in main_quest_flags:
		if not character.get_player_data("quest_flags", {}).has(flag):
			return false
	
	return true

func get_access_requirements() -> Array[String]:
	"""Get list of requirements to access this region"""
	var requirements = []
	
	if recommended_level > 1:
		requirements.append("Level " + str(recommended_level) + " recommended")
	
	for flag in main_quest_flags:
		requirements.append("Quest: " + flag.replace("_", " ").capitalize())
	
	return requirements

func discover():
	"""Mark this region as discovered"""
	if not is_discovered:
		is_discovered = true
		print("Discovered region: ", display_name)

func get_available_npcs() -> Array[String]:
	"""Get NPCs currently available in this region"""
	# This could be filtered by time of day, story progress, etc.
	return npcs.duplicate()

func get_available_quests(character: CharacterResource) -> Array[String]:
	"""Get quests available to a character in this region"""
	var available = []
	
	for quest_id in side_quests:
		# Check if quest is available based on character progress
		# This would need a quest system to properly implement
		available.append(quest_id)
	
	return available

func get_enemy_spawn_data(area_type: String = "") -> Array[Dictionary]:
	"""Get enemy spawn data for this region"""
	if area_type == "":
		return enemy_spawns.duplicate()
	
	var filtered = []
	for spawn in enemy_spawns:
		if spawn.get("area_type", "") == area_type:
			filtered.append(spawn)
	
	return filtered

func get_random_encounter() -> Dictionary:
	"""Get a random encounter for this region"""
	if random_encounters.size() == 0:
		return {}
	
	# Simple random selection - could be weighted by probability
	var index = randi() % random_encounters.size()
	return random_encounters[index]

func should_trigger_random_encounter() -> bool:
	"""Check if a random encounter should trigger"""
	if random_encounters.size() == 0:
		return false
	
	# Base chance modified by danger level
	var base_chance = 0.1  # 10% base chance
	var danger_modifier = danger_level * 0.05
	var total_chance = base_chance + danger_modifier
	
	return randf() < total_chance

func get_harvestable_resources() -> Array[String]:
	"""Get resources that can be harvested in this region"""
	return harvestable_resources.duplicate()

func get_treasure_at_location(location: Vector2) -> Dictionary:
	"""Get treasure data at a specific location"""
	for treasure in treasure_locations:
		var treasure_pos = treasure.get("position", Vector2.ZERO)
		var distance = location.distance_to(treasure_pos)
		if distance < treasure.get("radius", 50):
			return treasure
	
	return {}

func add_npc(npc_id: String):
	"""Add an NPC to this region"""
	if not npcs.has(npc_id):
		npcs.append(npc_id)

func remove_npc(npc_id: String):
	"""Remove an NPC from this region"""
	npcs.erase(npc_id)

func add_quest(quest_id: String):
	"""Add a quest to this region"""
	if not side_quests.has(quest_id):
		side_quests.append(quest_id)

func complete_quest(quest_id: String):
	"""Mark a quest as completed and remove it"""
	side_quests.erase(quest_id)

func get_corruption_effects() -> Array[String]:
	"""Get effects of corruption in this region"""
	var effects = []
	
	if corruption_level > 20:
		effects.append("Dark magic is stronger here")
	if corruption_level > 40:
		effects.append("Undead creatures are more common")
	if corruption_level > 60:
		effects.append("The very air feels tainted")
	if corruption_level > 80:
		effects.append("Reality seems unstable")
	
	return effects

func get_weather_description() -> String:
	"""Get description of current weather"""
	match weather_type:
		WeatherType.CLEAR:
			return "Clear skies"
		WeatherType.CLOUDY:
			return "Overcast"
		WeatherType.RAIN:
			return "Light rain"
		WeatherType.STORM:
			return "Thunderstorm"
		WeatherType.FOG:
			return "Dense fog"
		WeatherType.SNOW:
			return "Snowfall"
		WeatherType.BLIZZARD:
			return "Blizzard"
		WeatherType.SUPERNATURAL:
			return "Unnatural weather"
		_:
			return "Unknown weather"

func get_time_description() -> String:
	"""Get description of current time"""
	match time_of_day:
		TimeOfDay.DAWN:
			return "Dawn"
		TimeOfDay.DAY:
			return "Daytime"
		TimeOfDay.DUSK:
			return "Dusk"
		TimeOfDay.NIGHT:
			return "Night"
		TimeOfDay.ETERNAL_NIGHT:
			return "Eternal night"
		TimeOfDay.TIMELESS:
			return "Time seems meaningless here"
		_:
			return "Unknown time"

func get_atmosphere_description() -> String:
	"""Get full atmospheric description"""
	var desc = get_weather_description() + ", " + get_time_description()
	
	match lighting_mood:
		LightingMood.BRIGHT:
			desc += ". The area is well-lit and welcoming."
		LightingMood.DIM:
			desc += ". Shadows gather in the corners."
		LightingMood.DARK:
			desc += ". Darkness pervades the area."
		LightingMood.OMINOUS:
			desc += ". An ominous feeling hangs in the air."
		LightingMood.MAGICAL:
			desc += ". Magical energy crackles in the atmosphere."
		LightingMood.CORRUPTED:
			desc += ". The very air feels tainted and wrong."
	
	return desc

func to_dictionary() -> Dictionary:
	"""Convert to dictionary for saving"""
	var data = super.to_dictionary()
	data.merge({
		"region_type": region_type,
		"difficulty_level": difficulty_level,
		"recommended_level": recommended_level,
		"is_discovered": is_discovered,
		"is_accessible": is_accessible,
		"background_music": background_music,
		"ambient_sound": ambient_sound,
		"weather_type": weather_type,
		"time_of_day": time_of_day,
		"lighting_mood": lighting_mood,
		"towns": towns,
		"dungeons": dungeons,
		"points_of_interest": points_of_interest,
		"travel_connections": travel_connections,
		"npcs": npcs,
		"enemy_spawns": enemy_spawns,
		"random_encounters": random_encounters,
		"harvestable_resources": harvestable_resources,
		"treasure_locations": treasure_locations,
		"merchant_inventories": merchant_inventories,
		"main_quest_flags": main_quest_flags,
		"side_quests": side_quests,
		"story_events": story_events,
		"corruption_level": corruption_level,
		"danger_level": danger_level,
		"magical_influence": magical_influence
	})
	return data

func from_dictionary(data: Dictionary):
	"""Load from dictionary"""
	super.from_dictionary(data)
	region_type = data.get("region_type", RegionType.OVERWORLD)
	difficulty_level = data.get("difficulty_level", 1)
	recommended_level = data.get("recommended_level", 1)
	is_discovered = data.get("is_discovered", false)
	is_accessible = data.get("is_accessible", true)
	background_music = data.get("background_music", "")
	ambient_sound = data.get("ambient_sound", "")
	weather_type = data.get("weather_type", WeatherType.CLEAR)
	time_of_day = data.get("time_of_day", TimeOfDay.DAY)
	lighting_mood = data.get("lighting_mood", LightingMood.NEUTRAL)
	towns = data.get("towns", [])
	dungeons = data.get("dungeons", [])
	points_of_interest = data.get("points_of_interest", [])
	travel_connections = data.get("travel_connections", [])
	npcs = data.get("npcs", [])
	enemy_spawns = data.get("enemy_spawns", [])
	random_encounters = data.get("random_encounters", [])
	harvestable_resources = data.get("harvestable_resources", [])
	treasure_locations = data.get("treasure_locations", [])
	merchant_inventories = data.get("merchant_inventories", {})
	main_quest_flags = data.get("main_quest_flags", [])
	side_quests = data.get("side_quests", [])
	story_events = data.get("story_events", [])
	corruption_level = data.get("corruption_level", 0)
	danger_level = data.get("danger_level", 1)
	magical_influence = data.get("magical_influence", 0)
