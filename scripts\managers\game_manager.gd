extends Node

## GameManager - Core singleton for managing game state and coordination
##
## This is the main brain of our game. It handles:
## - Game state management (menu, playing, paused, etc.)
## - Player data and progression
## - Save/Load functionality
## - Global game settings
## - Coordination between other managers

signal game_state_changed(new_state: GameState)
signal player_data_changed()

enum GameState {
	MENU,
	LOADING,
	PLAYING,
	PAUSED,
	INVENTORY,
	DIALOGUE,
	DUNGEON,
	GAME_OVER
}

# Current game state
var current_state: GameState = GameState.MENU

# Player data - this will grow as we add more systems
var player_data: Dictionary = {
	"name": "<PERSON><PERSON><PERSON>",
	"level": 1,
	"experience": 0,
	"health": 100,
	"max_health": 100,
	"stamina": 100,
	"max_stamina": 100,
	"corruption": 0,  # Dark magic corruption level
	"alignment": 0,   # Moral alignment (-100 evil to +100 good)
	"current_region": "ashfallow_moor",
	"current_location": "grimhallow",
	"inventory": [],
	"equipment": {},
	"abilities": [],
	"quest_progress": {},
	"discovered_locations": ["grimhallow"],
	"completed_dungeons": []
}

# Game settings
var game_settings: Dictionary = {
	"master_volume": 1.0,
	"music_volume": 0.8,
	"sfx_volume": 1.0,
	"difficulty": "normal",
	"auto_save": true,
	"show_damage_numbers": true,
	"camera_shake": true
}

func _ready():
	print("GameManager initialized")
	# Load settings and player data if they exist
	load_settings()
	
func change_game_state(new_state: GameState):
	"""Change the current game state and notify listeners"""
	if current_state != new_state:
		var old_state = current_state
		current_state = new_state
		print("Game state changed from ", GameState.keys()[old_state], " to ", GameState.keys()[new_state])
		game_state_changed.emit(new_state)

func get_current_state() -> GameState:
	"""Get the current game state"""
	return current_state

func is_game_playing() -> bool:
	"""Check if the game is in a playing state"""
	return current_state in [GameState.PLAYING, GameState.DUNGEON]

func is_game_paused() -> bool:
	"""Check if the game is paused or in a menu state"""
	return current_state in [GameState.PAUSED, GameState.INVENTORY, GameState.DIALOGUE]

# Player data management
func update_player_data(key: String, value):
	"""Update a specific piece of player data"""
	player_data[key] = value
	player_data_changed.emit()
	
	# Auto-save if enabled
	if game_settings.auto_save:
		save_game()

func get_player_data(key: String, default_value = null):
	"""Get a specific piece of player data"""
	return player_data.get(key, default_value)

func add_experience(amount: int):
	"""Add experience and handle level ups"""
	player_data.experience += amount
	check_level_up()
	player_data_changed.emit()

func check_level_up():
	"""Check if player should level up (simple formula for now)"""
	var required_exp = player_data.level * 100
	if player_data.experience >= required_exp:
		player_data.level += 1
		player_data.experience -= required_exp
		player_data.max_health += 10
		player_data.max_stamina += 5
		player_data.health = player_data.max_health  # Full heal on level up
		print("Level up! Now level ", player_data.level)

func modify_corruption(amount: int):
	"""Modify corruption level (affects magic and story)"""
	player_data.corruption = clamp(player_data.corruption + amount, 0, 100)
	player_data_changed.emit()

func modify_alignment(amount: int):
	"""Modify moral alignment"""
	player_data.alignment = clamp(player_data.alignment + amount, -100, 100)
	player_data_changed.emit()

# Save/Load system (basic implementation)
func save_game():
	"""Save the current game state"""
	var save_data = {
		"player_data": player_data,
		"timestamp": Time.get_unix_time_from_system()
	}
	
	var save_file = FileAccess.open("user://savegame.dat", FileAccess.WRITE)
	if save_file:
		save_file.store_string(JSON.stringify(save_data))
		save_file.close()
		print("Game saved successfully")
	else:
		print("Error: Could not save game")

func load_game() -> bool:
	"""Load the saved game state"""
	var save_file = FileAccess.open("user://savegame.dat", FileAccess.READ)
	if save_file:
		var save_data_text = save_file.get_as_text()
		save_file.close()
		
		var json = JSON.new()
		var parse_result = json.parse(save_data_text)
		
		if parse_result == OK:
			var save_data = json.data
			player_data = save_data.player_data
			player_data_changed.emit()
			print("Game loaded successfully")
			return true
		else:
			print("Error: Could not parse save file")
			return false
	else:
		print("No save file found")
		return false

func save_settings():
	"""Save game settings"""
	var settings_file = FileAccess.open("user://settings.dat", FileAccess.WRITE)
	if settings_file:
		settings_file.store_string(JSON.stringify(game_settings))
		settings_file.close()

func load_settings():
	"""Load game settings"""
	var settings_file = FileAccess.open("user://settings.dat", FileAccess.READ)
	if settings_file:
		var settings_text = settings_file.get_as_text()
		settings_file.close()
		
		var json = JSON.new()
		var parse_result = json.parse(settings_text)
		
		if parse_result == OK:
			game_settings = json.data
			print("Settings loaded successfully")
		else:
			print("Error: Could not parse settings file")

func quit_game():
	"""Quit the game with proper cleanup"""
	save_settings()
	if game_settings.auto_save:
		save_game()
	get_tree().quit()
